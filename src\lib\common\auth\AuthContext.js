import React, { createContext } from "react";
import PropTypes from "prop-types";

// Context to make user authorization information available throughout application.
const AuthContext = createContext();
const AuthContextWrapper = (props) => {
  const { authUser, authSuccess, children } = props;

  const isAuthenticated = () => {
    if (authSuccess) {
      return true;
    }
    return false;
  };

  // Assumption is authUser contains a 'roles' attribute. The allowedRoles can be an
  // array of roles or a string with a single role value. See hook "useAuth" which is
  // responsible for returning the authUser.
  const isInRole = (allowedRoles) => {
    if (
      !authUser ||
      !authUser.roles ||
      authUser.roles.length === 0 ||
      authUser.roles.constructor !== Array
    ) {
      return false;
    }
    let matchingUserRoles = [];
    authUser.roles.filter((role) => {
      if (
        !!allowedRoles &&
        allowedRoles != null &&
        allowedRoles.constructor === Array
      ) {
        // Array
        let ar = allowedRoles.find(
          (k) => k.toLowerCase().trim() === role.toLowerCase().trim()
        );
        if (ar) {
          matchingUserRoles.push(role);
        }
      } else {
        // String
        if (
          !!allowedRoles &&
          allowedRoles != null &&
          allowedRoles.toLowerCase().trim() === role.toLowerCase().trim()
        ) {
          matchingUserRoles.push(role);
        }
      }
      return role;
    });
    if (matchingUserRoles.length > 0) {
      return true;
    }
    return false;
  };

  // make the context object:
  const authContext = {
    authUser,
    isInRole,
    isAuthenticated,
  };
  return (
    <AuthContext.Provider value={authContext}>{children}</AuthContext.Provider>
  );
};

// proptypes
AuthContext.Provider.propTypes = {
  authUser: PropTypes.object,
  authSuccess: PropTypes.bool,
};

// default props
AuthContext.Provider.defaultProps = {
  authUser: {},
  authSuccess: false,
};

export { AuthContext, AuthContextWrapper };
