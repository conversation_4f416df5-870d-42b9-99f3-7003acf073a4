// Add global config values
const envGlobal = {
  templateAppName: "TPLD", // template will use if existing
  templateCip: "TI Confidential - NDA Restrictions", // template will use if existing
  templateShowIcon: true, // template will use if existing
  isAutoOpenSidebar: false, // if true, menu will auto open at 'lg' breakpoint
  drawerWidth: 300, // adjust width of drawer
  basePath: "/tpld-ui",
  helpPageURL:
    "https://confluence.itg.ti.com/display/SimbaInfo/Simba+User+Communications+Home",
  roleServiceAppName: "Simba",
};

// Add environment-specific config values
const envLocalhost = {
  templateEnvMessage: "*** Localhost ***",
  apiGatewayUrl: "http://simba-playground.itg.ti.com",
  apiGatewayLogoutUrl:
    "http://simba-playground.itg.ti.com/oidc/logout?returnUrl=http://localhost.dhcp.ti.com:3000",
};

const envDev = {
  templateEnvMessage: "*** Dev ***",
  apiGatewayUrl: "https://dms-dev.leokdd.itg.ti.com",
  apiGatewayLogoutUrl:
    "https://dms-dev.leokdd.itg.ti.com/oidc/logout?returnUrl=https://dms-dev.leokdd.itg.ti.com" +
    envGlobal.basePath,
};

const envStage = {
  templateEnvMessage: "*** Stage ***",
  apiGatewayUrl: "https://dms-dev.leokdd.itg.ti.com",
  apiGatewayLogoutUrl:
    "https://dms-dev.leokdd.itg.ti.com/oidc/logout?returnUrl=https://dms-dev.leokdd.itg.ti.com" +
    envGlobal.basePath,
};

const envProd = {
  apiGatewayUrl: "https://dms.leokdp.itg.ti.com",
  apiGatewayLogoutUrl:
    "https://dms.leokdp.itg.ti.com/oidc/logout?returnUrl=https://dms.leokdp.itg.ti.com" +
    envGlobal.basePath,
};

let envCurrent;

// Modify switch if environments are added/removed
// eslint-disable-next-line no-undef
switch (process.env.REACT_APP_ENV) {
  case "dev":
    envCurrent = envDev;
    break;
  case "stage":
    envCurrent = envStage;
    break;
  case "prod":
    envCurrent = envProd;
    break;
  default:
    envCurrent = envLocalhost;
}

export default {
  ...envGlobal,
  ...envCurrent,
};
