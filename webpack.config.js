const webpackMerge = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");
const path = require("path");
const Dotenv = require("dotenv-webpack");
const { EnvironmentPlugin } = require("webpack");

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "ti",
    projectName: "tpld-ui",
    webpackConfigEnv,
    argv,
  });

  return webpackMerge.smart(defaultConfig, {
    // modify the webpack config however you'd like to by adding to this object
    module: {
      rules: [
        {
          test: /\.(png|jpe?g|gif|svg)$/i,
          use: [
            {
              loader: "file-loader",
            },
          ],
        },
        {
          test: /\.scss$/,
          use: [
            "style-loader",
            {
              loader: "css-loader",
              options: {
                sourceMap: true,
              },
            },
            "resolve-url-loader",
            {
              loader: "sass-loader",
              options: {
                sourceMap: true,
              },
            },
          ],
        },
        {
          test: /\.m?js$/,
          exclude: /(node_modules|bower_components)/,
          use: {
            loader: "babel-loader",
            options: {
              plugins: ["lodash"],
              presets: ["@babel/preset-env"],
            },
          },
        },
      ],
    },
    plugins: [new Dotenv(), new EnvironmentPlugin(["REACT_APP_ENV"])],
    externals: ["react", "react-dom", "@ti/simba-common-util"],
    resolve: {
      alias: {
        // eslint-disable-next-line no-undef
        src: path.resolve(__dirname, "src"),
      },
    },
  });
};
