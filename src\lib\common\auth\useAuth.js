import { useState, useEffect } from "react";
import { UserUtil } from "@ti/simba-common-util";
import buildEnv from "../../../buildEnvironment";

// Hook to retrieve user authorization information.
//
// Makes a fetch call to an API that is behind a Zuul proxy service. The API and Zuul proxy service
// are documented at http://react.itg.ti.com. The proxy service will throw a 401 HTTP response
// if the user is not authenticated. If the user is authenticated, then the API response is an authUser object
// formatted as => {uid: 'a0900000', name: 'Hal 9000', roles: ['MACHINE', 'ADMIN']}
//
// There are a few opinionated components that depend on the authUser object as formatted above.
// If you do not wish to use the authUser object as-is, then modify or remove those components.
//  > AuthContext (common component)
//  > RouteManager (common component)
//  > ProfileSetup (sample component)
//  > Profile (sample component in navigation)
const authUserDefault = { uid: "", name: "", roles: [] };
const useAuth = () => {
  // If authLoading is true => the loading process is still in progress.
  const [authLoading, setAuthLoading] = useState(true);
  // If authSuccess is true => loading process has completed and was successful.
  const [authSuccess, setAuthSuccess] = useState(false);
  // If authUser is not null => object containing uid, name and roles.
  const [authUser, setAuthUser] = useState(authUserDefault);

  const successHandler = (userDetails) => {
    const data = {
      uid: userDetails.getId(),
      name: userDetails.getName(),
      roles: userDetails.getRoleNamesByApp(buildEnv.roleServiceAppName),
    };
    setAuthSuccess(true);
    setAuthUser(data);
    setAuthLoading(false);
    return data;
  };

  useEffect(() => {
    // Subscribe to user information from Simba common util
    const subscription = UserUtil.getUserDetail$().subscribe(successHandler);
    return () => subscription.unsubscribe();
  }, []);

  return [authLoading, authSuccess, authUser];
};

export default useAuth;
