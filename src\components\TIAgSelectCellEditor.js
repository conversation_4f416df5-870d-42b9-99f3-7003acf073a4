import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Theme, useTheme } from "@material-ui/core/styles";
import Select, { Styles } from "react-select";
import Color from "color";
import { ICellEditorParams } from "ag-grid-community";

/** @param {Theme} theme */
const getSelectTheme = (theme) => {
  return {
    /*
     * multiValue(remove)/color:hover
     */
    danger: theme.palette.text.primary,

    /*
     * multiValue(remove)/backgroundColor(focused)
     * multiValue(remove)/backgroundColor:hover
     */
    dangerLight: theme.palette.grey[200],

    /*
     * control/backgroundColor
     * menu/backgroundColor
     * option/color(selected)
     */
    // neutral0: theme.palette.background.default,
    neutral0: theme.palette.background.paper,

    /*
     * control/backgroundColor(disabled)
     */
    neutral5: "orange",

    /*
     * control/borderColor(disabled)
     * multiValue/backgroundColor
     * indicators(separator)/backgroundColor(disabled)
     */
    neutral10: "pink",

    /*
     * control/borderColor
     * option/color(disabled)
     * indicators/color
     * indicators(separator)/backgroundColor
     * indicators(loading)/color
     */
    neutral20: theme.palette.grey["A200"],

    /*
     * control/borderColor(focused)
     * control/borderColor:hover
     */
    // this should be the white, that's normally selected
    neutral30: theme.palette.text.primary,

    /*
     * menu(notice)/color
     * singleValue/color(disabled)
     * indicators/color:hover
     */
    neutral40: theme.palette.warning.main,

    /*
     * placeholder/color
     */
    // seen in placeholder text
    neutral50: theme.palette.grey["A200"],

    /*
     * indicators/color(focused)
     * indicators(loading)/color(focused)
     */
    neutral60: theme.palette.text.primary,
    neutral70: theme.palette.text.primary,

    /*
     * input/color
     * multiValue(label)/color
     * singleValue/color
     * indicators/color(focused)
     * indicators/color:hover(focused)
     */
    neutral80: theme.palette.text.primary,

    // no idea
    neutral90: "pink",

    /*
     * control/boxShadow(focused)
     * control/borderColor(focused)
     * control/borderColor:hover(focused)
     * option/backgroundColor(selected)
     * option/backgroundColor:active(selected)
     */
    // primary: theme.palette.text.primary,
    primary: Color(theme.palette.background.paper).lighten(1).hex(),

    /*
     * option/backgroundColor(focused)
     */
    primary25: Color(theme.palette.background.paper).lighten(0.25).hex(),

    /*
     * option/backgroundColor:active
     */
    primary50: Color(theme.palette.background.paper).lighten(0.5).hex(),
    primary75: Color(theme.palette.background.paper).lighten(0.75).hex(),
  };
};

export const TIAgSelectCellEditor = forwardRef(
  /**
   * @param {ICellEditorParams} props Component props.
   */
  (props, ref) => {
    const { api, column, charPress, node, rowIndex, options } = props;
    const [editing, setEditing] = useState(true);
    const [value, setValue] = useState(props.value);
    const [defaultOption] = useState(
      options.find((item) => item.value == props.value)
    );
    const [defaultInput] = useState(charPress != null ? charPress : undefined);
    const refInput = useRef(null);

    const theme = useTheme();
    const themeColors = getSelectTheme(theme);
    const dark = theme.palette.type === "dark";

    /** @type {Styles} */
    const styles = {
      container: (css) => ({
        ...css,
        lineHeight: "normal",
        width: column.getActualWidth() + "px",
      }),
      control: (css) => ({
        ...css,
        minHeight: "26px",
      }),
      menu: (css) => ({
        ...css,
        marginTop: "2px",
        marginBottom: "2px",
        // top: "auto",
        // bottom: "100%",
      }),
      valueContainer: (css) => ({
        ...css,
        padding: "0px 4px",
      }),
      indicatorSeparator: (css) => ({
        ...css,
        marginTop: "2px",
        marginBottom: "2px",
      }),
      clearIndicator: (css) => ({
        ...css,
        padding: "0px",
      }),
      dropdownIndicator: (css) => ({
        ...css,
        padding: "0px",
      }),
    };

    /* Component Editor Lifecycle methods */
    useImperativeHandle(ref, () => {
      return {
        // the final value to send to the grid, on completion of editing
        getValue() {
          return value;
        },

        // Gets called once before editing starts, to give editor a chance to
        // cancel the editing before it even starts.
        isCancelBeforeStart() {
          return false;
        },

        // Gets called once when editing is finished (eg if Enter is pressed).
        // If you return true, then the result of the edit will be ignored.
        isCancelAfterEnd() {
          return false;
        },
      };
    });

    const onBlur = () => {
      setEditing(false);
    };

    const onChange = (selectValue, actionMeta) => {
      if (selectValue) {
        setValue(selectValue.value);
        setEditing(false);
      } else {
        setValue(null);
        setEditing(false);
      }
    };

    useEffect(() => {
      // focus on the input
      setTimeout(() => refInput.current.focus());
    }, []);

    useEffect(() => {
      if (!editing) {
        api.stopEditing();
        api.setFocusedCell(rowIndex, column, node.rowPinned);
      }
    }, [api, column, editing, node.rowPinned, rowIndex]);

    return (
      <Select
        ref={refInput}
        styles={styles}
        defaultInputValue={defaultInput}
        defaultValue={defaultOption}
        defaultMenuIsOpen={true}
        isClearable
        onBlur={onBlur}
        onChange={onChange}
        options={options}
        theme={(theme) =>
          dark
            ? {
                ...theme,
                colors: { ...themeColors },
              }
            : theme
        }
      />
    );
  }
);

export default TIAgSelectCellEditor;
