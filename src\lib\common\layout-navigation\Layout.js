import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import config from "../../../buildEnvironment";
import clsx from "clsx";
import { makeStyles, useTheme } from "@material-ui/styles";
import { useMediaQuery } from "@material-ui/core";
import Sidebar from "./Sidebar";
import Topbar from "./Topbar";

const { drawerWidth, isAutoOpenSidebar } = config;

const useStyles = makeStyles((theme) => ({
  root: {
    paddingTop: window.ecosystem ? undefined : 42, // content begins
    height: "100%",
  },
  shiftContent: {
    paddingLeft: drawerWidth, // should match Sidebar.drawerWidth
  },
  content: {
    height: "100%",
  },
  hidden: {
    display: "none",
  },
}));

const Layout = (props) => {
  const { children, ...rest } = props;

  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up("lg"), {
    defaultMatches: true,
  }); // auto open breakpoint
  const isAutoDesktop = isDesktop && isAutoOpenSidebar ? true : false; // auto menu if both isAutoOpenSidebar and isDesktop
  const [openSidebar, setOpenSidebar] = useState(false);
  const shouldOpenSidebar = isAutoDesktop ? true : openSidebar;

  const handleSidebarOpen = () => {
    setOpenSidebar(true);
  };

  const handleSidebarClose = () => {
    // Set simba navtoggle to false
    if (window.ecosystem) {
      window.ecosystem.navToggle.next({ open: false });
    } else {
      setOpenSidebar(false);
    }
  };

  // Subscribe to simba navtoggle events
  useEffect(() => {
    let mounted = true;
    let observable = null;
    if (mounted) {
      if (window.ecosystem) {
        observable = window.ecosystem.navToggle.subscribe({
          next: (val) => setOpenSidebar(val.open),
        });
      }
    }
    return () => {
      if (observable) observable.unsubscribe();
      mounted = false;
    };
  }, []);

  return (
    <div
      className={clsx({
        [classes.root]: true,
        [classes.shiftContent]: isAutoDesktop,
      })}
    >
      <div
        className={clsx({
          [classes.hidden]: window.ecosystem,
        })}
      >
        <Topbar
          {...rest}
          onSidebarOpen={handleSidebarOpen}
          isAutoDesktop={isAutoDesktop}
        />
      </div>
      <Sidebar
        onClose={handleSidebarClose}
        open={shouldOpenSidebar}
        variant={isAutoDesktop ? "persistent" : "temporary"}
      />
      <main className={classes.content}>{children}</main>
    </div>
  );
};

Layout.propTypes = {
  children: PropTypes.node,
};

export default Layout;
