# Template for Simba App

Template of application for Simba framework. This application is meant to be deployed into a [single-spa](https://single-spa.js.org/) environment.

## Prerequisites

- Node.js 14.15.3+
- Yarn 2+ CLI or NPM CLI

## Installation

Install dependencies using Yarn or NPM:

```shell script
yarn install
```
or
```shell script
npm install
```

## Usage

> **NOTE:** This application is meant to be run in a single-spa environment.

Run in localhost using Yarn or NPM:

```shell script
yarn start
```
or
```shell script
npm start
```

Navigate to the Simba Playground: http://simba-playground.itg.ti.com/playground/instant-test?name=@ti/tpld-ui&url=3000 to preview the application.
Or use the Local Playground: http://localhost.dhcp.ti.com:10000/playground/instant-test?name=@ti/tpld-ui&url=3003

## Customization

To customize the application:

1. Globally replace all occurrences of "simba-app-template" in the workspace with the desired application name. The following files should be updated:
    * package.json
    * package-lock.json
    * README.md (update parameter value in the URL)
    * webpack.config.js
    * buildEnvironment.js
    * src/ti-simba-app-template.js (rename the file)

2. Create new components in the "src/components" directory as needed.

3. Update "src/routes.js" to map URL paths to new components created in the previous step.

4. Update "src/root.component.js" to customize the top horizontal toolbar options. See [Documentation](https://confluence.itg.ti.com/display/PLUE/Simba+Common+Header#SimbaCommonHeader-ConfigureApp-specificToolbar) for more details.
