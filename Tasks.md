### Task 1: Adding "Order Type" Column to the Dashboard

#### Objective
Add a new column named **"Order Type"** to the dashboard.

#### Steps Taken

1. **Frontend Analysis**
   - Identified that the dashboard screen was implemented in `ReqTable.js`.
   - Determined that changes were required in this file to add the new column.
   - Traced the data source for the dashboard, which was being loaded from the API endpoint:
     `/tpld-api/questionnaires/TPLD_CUSTOM_OPN`.

2. **Backend Exploration**
   - Located the API implementation in the backend. The endpoint `/questionnaires/{txType}` was defined in `QsnController.java`.
   - Investigated the service layer (`OtpQsnService.java`) and DAO layer (`OtpQsnDao.xml`) to understand how the data was being fetched.
   - Found that the data was being retrieved from the view `V_OTP_QSN`, aliased as `QSN`.

3. **Database Analysis**
   - Traced the view `V_OTP_QSN` to its underlying table `OTP_QSN` in the file `view-questionnaire.sql`.
   - Logged into the database using Toad to inspect the `OTP_QSN` table.
   - Verified that the table's attributes matched the dashboard data for confirmation.
   - Found that the `orderType` information was nested within the `QSN_DATA` column under the structure:
     `order -> questionnaire -> orderType`.

4. **Implementation**
   - Added a new column in `ReqTable.js` with the following properties:
     - **Header**: "Order Type"
     - **Field**: `qsnData.order.questionnaire.orderType`.

#### Outcome
The "Order Type" column was successfully added to the dashboard, displaying the required data from the backend.

---

### Task 2: Renaming "Estimated Volume" Field on the Individual Request Page

#### Objective
Rename the **"Estimated Volume"** field to **"Yearly Estimated Volume"** on the individual request page.

#### Steps Taken

1. **Frontend Analysis**
   - Identified that the individual request page was implemented in `ReqView.js`.
   - Located the **"Estimated Volume"** field in the file.

2. **Implementation**
   - Renamed the field from **"Estimated Volume"** to **"Yearly Estimated Volume"** in `ReqView.js`.
   - Verified that the field was fetching data from `questionnaire.volumeEst`, which is nested in the `QSN_DATA` column under the structure:
     `order -> questionnaire -> volumeEst`.

#### Outcome
The field was successfully renamed to **"Yearly Estimated Volume"** on the individual request page.

---

### Task 3: Making WW Account ID Optional for "samples" Order Type

#### Objective
Allow task creation for **"samples"** order type without requiring the **WW Account ID**. For other order types, the **WW Account ID** remains mandatory.

#### Part 1: Manual Task Creation

##### Steps Taken

1. **Backend Analysis**
   - Identified that the API endpoint `/questionnaires` was responsible for creating questionnaires.
   - Located the implementation in the `QsnController.java` file under the method:
     `@PostMapping("/questionnaires")`.

2. **Code Exploration**
   - Found that the `createQsn` method in `OtpQsnService.java` was responsible for:
     - Saving the questionnaire in the database.
     - Looking up the **WW Account ID** and updating the database.
     - Sending an email.
     - Creating a task if the **WW Account ID** was present.
   - Verified that the **order type** was stored in the `qsnData` field of the `OtpQsn` object, under the structure:
     `qsnData -> order -> questionnaire -> orderType`.

3. **Implementation**
   - Extracted the **order type** from the `qsnData` field in the `createQsn` method.
   - Added an `if-else` condition to handle task creation:
     - If the **order type** is **"samples"**, the task is created without requiring the **WW Account ID**.
     - For other order types, the existing logic remains unchanged, requiring the **WW Account ID** for task creation.
   - Ensured that the **WW Account ID** lookup and database update still occur for all order types.

##### Outcome
The feature was successfully implemented, allowing task creation for **"samples"** order type without requiring the **WW Account ID**, while maintaining the existing behavior for other order types.

#### Part 2: Scheduled Job Modifications

##### Background
A scheduled job exists in the system that runs periodically to:
- Lookup missing WW Account IDs for applicable questionnaires
- Create Marketing Review tasks for questionnaires with WW Account IDs
- Send follow-up emails for questionnaires that couldn't be refreshed

The scheduled job is implemented with the following cron expression:
```java
@Scheduled(cron = "${app.ww-acc-id.refresh.cron.expression}")
public void executeRefresh() throws MessagingException, IOException
```

##### Steps Taken

1. **Code Analysis**
   - Located the scheduled job method `executeRefresh()` in `OtpQsnService.java`
   - Identified that the job performs three main operations:
     - Looks up missing WW Account IDs using `otpQsnDao.getQsnsWithoutWwAccIdByType(TPLD_TX_TYPE)`
     - Creates tasks for questionnaires with WW Account IDs
     - Sends follow-up emails using `otpQsnDao.getQsnsWithoutWwAccIdAndRequiringFollowUpByType()`

2. **Database Layer Modifications**
   - Modified the SQL queries in `OtpQsnDao.xml` to exclude sample orders from both:
     - Task creation process
     - Email notification process
   - Added the following condition to the relevant SQL queries:
     ```sql
     AND JSON_VALUE(QSN.QSN_DATA, '$.order.questionnaire.orderType') != 'samples'
     ```

3. **Functions Modified**
   - `getQsnsWithoutWwAccIdByType()` - Now excludes sample orders from WW Account ID lookup for task creation
   - `getQsnsWithoutWwAccIdAndRequiringFollowUpByType()` - Now excludes sample orders from follow-up email notifications

##### Outcome
The scheduled job now properly excludes sample orders from:
- Automatic task creation (even if WW Account ID is found)
- Follow-up email notifications for missing WW Account IDs

This ensures that sample orders are handled differently in both manual and automated processes, maintaining consistency across the entire system.

---

### Task 4: Implementing Pagination for the Dashboard

#### Objective
Implement pagination functionality for the dashboard to improve performance and user experience by loading data in smaller chunks instead of loading all records at once.

#### Backend Implementation

##### Steps Taken

1. **API Analysis**
   - Located the API endpoint `/questionnaires/{txType}` in `QsnController.java`
   - Identified that this endpoint was responsible for fetching all questionnaire data for the dashboard

2. **API Modifications**
   - Added two new query parameters to the API endpoint:
     - `pageSize` - Number of records to fetch per page
     - `offset` - Number of records to skip (for pagination)
   - Modified the API response structure to include:
     - `List<OtpQsn>` - The paginated list of questionnaires
     - Total count - Total number of questionnaires available

3. **Service Layer Changes**
   - Updated methods in `OtpQsnService.java` to accept pagination parameters
   - Modified the service to return both the paginated data and total count

4. **DAO Layer Modifications**
   - Updated `OtpQsnDao.java` interface to include pagination parameters
   - Modified `OtpQsnDao.xml` to implement pagination in SQL queries

5. **SQL Implementation**
   - Added pagination logic to the SQL query using Oracle syntax:
     ```sql
     ORDER BY QSN.CREATED_DATE DESC
     OFFSET #{offset} ROWS
     FETCH FIRST #{pageSize} ROWS ONLY
     ```
   - **Note**: Initially faced challenges with the SQL syntax as previous experience was with MySQL, but successfully adapted to Oracle's `OFFSET...FETCH` syntax

##### Backend Outcome
Successfully implemented server-side pagination that allows the API to return a specified number of records with proper offset handling, significantly improving performance for large datasets.

#### Frontend Implementation

##### Steps Taken

1. **Component Analysis**
   - Identified that `ReqTable.js` was the main component responsible for displaying the dashboard table
   - Determined that pagination controls needed to be added to this component

2. **API Integration**
   - Modified the API call in `ReqTable.js` to include pagination parameters (`pageSize` and `offset`)
   - Updated the component state to handle:
     - Current page data
     - Total record count
     - Current page number
     - Page size

3. **UI Implementation**
   - Added pagination controls to the table component:
     - **Previous** button for navigating to the previous page
     - **Next** button for navigating to the next page
     - Page information display (current page, total pages, etc.)

4. **State Management**
   - Implemented proper state management for:
     - Tracking current page
     - Handling page size changes
     - Managing loading states during page transitions

##### Frontend Outcome
Successfully implemented a user-friendly pagination interface that allows users to navigate through large datasets efficiently with Previous/Next navigation controls.

#### Overall Outcome
The pagination feature was successfully implemented across both backend and frontend, providing:
- **Improved Performance**: Reduced initial load time by fetching only required records
- **Scalability**: System can now handle larger datasets without performance degradation
