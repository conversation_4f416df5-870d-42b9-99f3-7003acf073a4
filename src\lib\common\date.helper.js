/**
 * String.prototype.padStart() polyfill
 * https://github.com/uxitten/polyfill/blob/master/string.polyfill.js
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/padStart
 */
if (!String.prototype.padStart) {
  String.prototype.padStart = function padStart(targetLength, padString) {
    targetLength = targetLength >> 0; //truncate if number or convert non-number to 0;
    padString = String(typeof padString !== "undefined" ? padString : " ");
    if (this.length > targetLength) {
      return String(this);
    } else {
      targetLength = targetLength - this.length;
      if (targetLength > padString.length) {
        padString += padString.repeat(targetLength / padString.length); //append to original to ensure we are longer than needed
      }
      return padString.slice(0, targetLength) + String(this);
    }
  };
}

/**
 * Formats date parameter to mm/dd/yy hh:mm am/pm. If we have more complex use cases I suggest installing moment.js
 * @param dateTime {Date} date parameter
 */
export const toFormattedDateTime = (dateTime) => {
  const minutes = dateTime?.getMinutes().toString().padStart(2, "0");
  const seconds = dateTime?.getSeconds().toString().padStart(2, "0");
  const hours = dateTime?.getHours().toString().padStart(2, "0");

  const month = (dateTime?.getMonth() + 1).toString().padStart(2, "0");
  const day = dateTime?.getDate().toString().padStart(2, "0");
  const year = dateTime?.getFullYear().toString().padStart(2, "0");
  return `${month}/${day}/${year} ${hours}:${minutes}:${seconds} CDT/CST`; // todo make time zone configurable???
};
