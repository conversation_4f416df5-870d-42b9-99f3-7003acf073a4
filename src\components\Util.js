import { createStyles, makeStyles, Theme } from "@material-ui/core/styles";
import { ColDef } from "ag-grid-community";
import Color from "color";
import { TIAgSelectCellEditor } from "./TIAgSelectCellEditor";
import { App, ExtApp, Me, Metadata, Project } from "./TypeDefs";

export const gridComponents = {
  TIAgSelectCellEditor: TIAgSelectCellEditor,
};

export const defaultGridTheme = "ag-theme-balham";

/** @type {Me} */
export const defaultMe = {
  Manager: false,
  UserId: "",
  UserName: "",
};

/** @type {Metadata} */
export const defaultMetadata = {
  AllocationTypes: [],
  Apps: [],
  Manager: false,
  Projects: [],
  ProjectApps: [],
  Roles: [],
  Users: [],
  UserId: "",
  UserName: "",
};

/**
 * @template TData
 * @param {ColDef<TData>} colDef
 * @returns {ColDef<TData>}
 */
export function createColDef(colDef) {
  return colDef;
}

/**
 * @param {string} error
 */
export function defaultErrorHandler(error) {
  console.error(error);
  alert(error);
}

/**
 * @template T
 * @param {string} url
 * @param {T} defaultResult
 * @param {string} errorMessage
 * @return {Promise<T>}
 */
export function fetchGetJson(url, defaultResult, errorMessage) {
  return fetch(url, { headers: { Accept: "application/json" } }).then(
    (response) => {
      if (!response.ok) {
        throw new Error(errorMessage || "API request failed");
      } else {
        return response
          .json()
          .then((data) => {
            if (data == null || data === "") {
              data = defaultResult;
            }
            return Promise.resolve(data);
          })
          .catch((reason) => {
            console.error(reason);
            throw new Error(errorMessage || "API response is not JSON");
          });
      }
    }
  );
}

/**
 * @param {string} url
 * @param {any} body
 * @return {Promise<any>}
 */
export function fetchPost(url, body) {
  return fetch(url, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify(body),
  });
}

/**
 * @template T
 * @param {string} url
 * @param {any} body
 * @param {T} defaultResult
 * @param {string} errorMessage
 * @return {Promise<T>}
 */
export function fetchPostJson(url, body, defaultResult, errorMessage) {
  return fetchPost(url, body).then((response) => {
    if (!response.ok) {
      throw new Error(errorMessage || "API request failed");
    } else {
      return response
        .json()
        .then((data) => {
          if (data == null || data === "") {
            data = defaultResult;
          }
          return Promise.resolve(data);
        })
        .catch((reason) => {
          console.error(reason);
          throw new Error(errorMessage || "API response is not JSON");
        });
    }
  });
}

/**
 * @param {string} pattern
 * @param {string} errorMessage
 * @returns {Array<App>}
 */
export function getApps(pattern, errorMessage) {
  let url = "/rpa-api/Apps";
  if (!isBlank(pattern)) {
    const search = new URLSearchParams();
    search.set("appName", pattern);
    url += "?" + search.toString();
  }
  /** @type {Array<App>} */
  const defaultResult = [];
  return fetchGetJson(url, defaultResult, errorMessage);
}

/**
 * @param {string} pattern
 * @param {string} errorMessage
 * @returns {Array<ExtApp>}
 */
export function getExtApps(pattern, errorMessage) {
  let url = "/rpa-api/ExtApp";
  if (!isBlank(pattern)) {
    const search = new URLSearchParams();
    search.set("extAppName", pattern);
    url += "?" + search.toString();
  }
  /** @type {Array<ExtApp>} */
  const defaultResult = [];
  return fetchGetJson(url, defaultResult, errorMessage);
}

/**
 * @param {Theme} theme
 * @returns {string}
 */
export function getGridTheme(theme) {
  return defaultGridTheme + (theme.palette.type === "dark" ? "-dark" : "");
}

/**
 * @param {string} pattern
 * @param {string} errorMessage
 * @returns {Array<Project>}
 */
export function getProjects(pattern, errorMessage) {
  let url = "/rpa-api/Projects";
  if (!isBlank(pattern)) {
    const search = new URLSearchParams();
    search.set("projName", pattern);
    url += "?" + search.toString();
  }
  /** @type {Array<Project>} */
  const defaultResult = [];
  return fetchGetJson(url, defaultResult, errorMessage);
}

/**
 * @param {Array<any>} values
 * @returns {boolean}
 */
export function isAnyBlank(...values) {
  for (let i = 0; i < values.length; i++) {
    if (isBlank(values[i])) {
      return true;
    }
  }
  return false;
}

/**
 * @param {any} value
 * @returns {boolean}
 */
export function isBlank(value) {
  return value == null || value === "";
}

export const useStyles = makeStyles((theme) => {
  const dark = theme.palette.type === "dark";
  return createStyles({
    fullHeight: {
      height: "100%",
      width: "100%",
      display: "flex",
      flexDirection: "column",
    },
    searchPanel: {
      display: "flex",
      gap: "1em",
      padding: "0.5em",
      alignItems: "center",
    },
    grayCell: {
      "&.ag-cell": {
        backgroundColor: dark ? "#333333" : "#E8E8E8",
      },
      ".ag-row.ag-row-hover &.ag-cell": {
        backgroundColor: dark ? Color("#333333").lighten(0.7).hex() : "#D7DADB",
      },
    },
    requestView: {
      width: "100%",
      "& th": {
        fontWeight: "bold",
        width: "250px",
      },
    },
    tealCell: {
      "&.ag-cell": {
        backgroundColor: dark ? "#115566" : "#D9F1F4",
      },
      ".ag-row.ag-row-hover &.ag-cell": {
        backgroundColor: dark ? Color("#115566").lighten(0.7).hex() : "#C9E3E7",
      },
    },
    tiAgGrid: {
      flexGrow: 1,
      fontSize: "13px",
      "& .ag-header": {
        backgroundColor: dark ? "#555555" : "#E8E8E8",
      },
      "& .ag-header-cell,.ag-header-group-cell,.ag-header-icon": {
        color: dark ? "#F7F7F7" : "#333333",
        fontSize: "13px",
        fontWeight: "normal",
      },
      "& .ag-header-cell input.ag-input-field-input": {
        color: dark ? theme.palette.secondary.contrastText : "black",
      },
    },
  });
});
