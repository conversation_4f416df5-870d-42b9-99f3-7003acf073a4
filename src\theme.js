import { createTheme } from "@material-ui/core/styles";
import { ThemeUtil } from "@ti/simba-common-util";

import typography from "./lib/common/theme/typography";
import overrides from "./lib/common/theme/overrides";
import palette from "./lib/common/theme/palette";
import props from "./lib/common/theme/props";

export const defaultTheme = createTheme({
  palette,
  typography,
  overrides,
});

export function getProfileTheme(userProfile) {
  const paletteName = userProfile.theme
    ? userProfile.theme.name
    : ThemeUtil.getDefaultPaletteName();
  const newPalette = ThemeUtil.getPalette(paletteName);
  if (newPalette) {
    return createTheme({
      palette: newPalette,
      typography,
      overrides,
      props,
    });
  } else {
    return defaultTheme;
  }
}

export default defaultTheme;
