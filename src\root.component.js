import React, { useEffect, useState } from "react";

import CssBaseline from "@material-ui/core/CssBaseline";
import { ThemeProvider } from "@material-ui/core/styles";
import AssignmentIcon from "@material-ui/icons/Assignment";

import DateFnsUtils from "@date-io/moment";
import { MuiPickersUtilsProvider } from "@material-ui/pickers";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";

import Routes from "./routes";
import { defaultTheme, getProfileTheme } from "./theme";
import buildEnv from "./buildEnvironment";

// noinspection NpmUsedModulesInstalled
import { AppToolbarUtil, UserUtil } from "@ti/simba-common-util";
import { useAuth } from "./lib/common/auth";

import { SimbaUserContextProvider } from "./lib/common/user/SimbaUserContext";
const { basePath, helpPageURL } = buildEnv;

import { NULL_USER_CONTEXT, UserContext } from "./components/UserContext";
import { defaultErrorHandler, fetchGetJson } from "./components/Util";

export default () => {
  const [authLoading, authSuccess, authUser] = useAuth();

  // Hook for detecting user profile changes (to switch theme)
  const [theme, setTheme] = useState(defaultTheme);

  const [userContext, setUserContext] = useState(NULL_USER_CONTEXT);

  useEffect(() => {
    const appToolbar = {
      basePath: basePath,
      helpPageURL: helpPageURL,
      disableSideBar: true,
      leftMenu: [
        {
          label: "TPLD Requests",
          href: "/req",
          icon: <AssignmentIcon />,
        },
      ],
      rightMenu: [],
      search: {
        enabled: false,
      },
    };
    // noinspection JSUnresolvedFunction
    AppToolbarUtil.setAppToolbarConfig(appToolbar);
  }, []);

  useEffect(() => {
    // Subscribe to user profile information from Simba common util
    const subscription = UserUtil.getUserProfile$().subscribe((userProfile) => {
      const newTheme = getProfileTheme(userProfile);
      setTheme(newTheme);
    });
    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    fetchGetJson(
      `/tpld-api/userContext`,
      NULL_USER_CONTEXT,
      "Cannot load user context"
    )
      .then(setUserContext)
      .catch(defaultErrorHandler);
  }, []);

  if (authLoading || !authSuccess || !userContext) {
    return <strong>Loading...</strong>;
  }
  return (
    <SimbaUserContextProvider>
      <UserContext.Provider value={{ userContext }}>
        <ThemeProvider theme={theme}>
          <MuiPickersUtilsProvider utils={DateFnsUtils}>
            <CssBaseline />
            <Routes />
          </MuiPickersUtilsProvider>
        </ThemeProvider>
      </UserContext.Provider>
    </SimbaUserContextProvider>
  );
};
