import React, { createContext, useEffect, useState } from "react";

/**
 * Context containing the currently logged in Simba User. TODO: maybe belongs in util class?
 * @type {React.Context<{}>}
 */
export const SimbaUserContext = createContext({});

const simbaUserUrl = "/simba/api/v1/user";

/**
 * Provides the Simba user data as a React context enabling user data to be consumed with `useContext` hook.
 * TODO: maybe belongs in util class?
 * <AUTHOR>
 */
export const SimbaUserContextProvider = ({ children }) => {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetch(simbaUserUrl, { method: "GET" })
      .then((response) => response.json())
      .then((json) => setUser(json));
    // todo on fail?
  }, []);

  return (
    <SimbaUserContext.Provider value={user}>
      {children}
    </SimbaUserContext.Provider>
  );
};
