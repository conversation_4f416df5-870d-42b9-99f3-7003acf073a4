import React, { useContext } from "react";
import { Link as RouterLink } from "react-router-dom";
import clsx from "clsx";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/styles";
import { Avatar, Typography } from "@material-ui/core";
import AccountCircle from "@material-ui/icons/AccountCircle";
import { AuthContext } from "src/lib/common";

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    minHeight: "fit-content",
  },
  avatar: {
    width: 50,
    height: 50,
    backgroundColor: theme.palette.primary.main,
  },
  name: {
    marginTop: theme.spacing(1),
  },
}));

const ProfileView = (props) => {
  const { className, ...rest } = props;
  const { authUser } = useContext(AuthContext);
  const classes = useStyles();
  const { name } = authUser;

  return (
    <div {...rest} className={clsx(classes.root, className)}>
      <Avatar
        alt="Account"
        className={classes.avatar}
        component={RouterLink}
        to="/account"
      >
        <AccountCircle style={{ fontSize: 50 }} />
      </Avatar>
      <Typography className={classes.name} variant="subtitle1">
        {name}
      </Typography>
    </div>
  );
};

ProfileView.propTypes = {
  className: PropTypes.string,
};

export default ProfileView;
