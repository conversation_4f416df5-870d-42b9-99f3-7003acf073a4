/**
 * @typedef {object} OtpQsn
 * @property {string} txType
 * @property {string} txId
 * @property {string} createdDate
 * @property {OtpQsnData} qsnData
 * @property {string} price
 * @property {string} opnNew
 * @property {string} wwAccountId
 */

/**
 * @typedef {object} OtpQsnData
 * @property {OtpQsnDataUserInfo} userInfo
 * @property {OtpQsnDataOrder} order
 */

/**
 * @typedef {object} OtpQsnDataOrder
 * @property {string} opn
 * @property {string} otp
 * @property {OtpQsnDataOrderQuestionnaire} questionnaire
 */

/**
 * @typedef {object} OtpQsnDataOrderQuestionnaire
 * @property {Array<string>} market
 * @property {string} marketOther
 * @property {string} endEquipment
 * @property {Array<string>} useCase
 * @property {string} useCaseOther
 * @property {boolean} military
 * @property {string} orderType
 * @property {number} sampAmount
 * @property {string} region
 * @property {string} firstName
 * @property {string} lastName
 * @property {string} addr1
 * @property {string} addr2
 * @property {string} townCity
 * @property {string} stateProvince
 * @property {string} zipPostal
 * @property {string} phoneNumber
 * @property {string} prodDate
 * @property {number} volumeEst
 * @property {boolean} ack
 */

/**
 * @typedef {object} OtpQsnDataUserInfo
 * @property {string} uid
 * @property {string} firstname
 * @property {string} lastname
 * @property {string} email
 * @property {string} permanentid
 * @property {string} tipassid
 * @property {string} userId
 */

/**
 * @typedef {object} OtpUserContext
 * @property {string} userId
 * @property {boolean} canEditPrice
 * @property {boolean} canEditWwAccId
 */

export {};
