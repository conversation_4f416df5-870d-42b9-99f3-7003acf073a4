import React, { useContext } from "react";
import Layout from "../layout-navigation/Layout";
import { AuthContext } from "../auth/AuthContext";
import PropTypes from "prop-types";
import { Typography } from "@material-ui/core";
import { Route, useHistory } from "react-router-dom";

/*
 * This component allows passing multiple props...
 *
 * :: Component
 * The component to render within the Layout.
 *
 * :: Roles
 * Checks if the user has the required role, if they do,
 * it renders the "component" prop. Otherwise it returns an unauthorized view.
 * If no roles are passed, then does not validate.
 *
 * Note: You shouldn't only rely on the client to protect your routes.
 * Make sure to implement security in your backend so only authorized users have
 * access to authorized data/methods. A good way to implement this is with Spring Method Security
 * https://www.baeldung.com/spring-security-method-security
 *
 * :: path
 * Required by the route component.
 */

const RouteManager = ({ component: Component, allowedRoles, ...rest }) => {
  const { isInRole } = useContext(AuthContext);
  const history = useHistory();
  if (allowedRoles) {
    // w/security check
    return (
      <Route
        {...rest}
        render={(props) =>
          isInRole(allowedRoles) ? (
            <Layout history={history}>
              <Component {...props} />
            </Layout>
          ) : (
            <Layout history={history}>
              <Typography align="center" variant="subtitle1">
                You are not authorized to view this page.
              </Typography>
            </Layout>
          )
        }
      />
    );
  }

  // w/o security check
  return (
    <Route
      {...rest}
      render={(props) => (
        <Layout history={history}>
          <Component history {...props} />
        </Layout>
      )}
    />
  );
};

RouteManager.propTypes = {
  component: PropTypes.any.isRequired,
  path: PropTypes.string,
  allowedRoles: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
};

export default RouteManager;
