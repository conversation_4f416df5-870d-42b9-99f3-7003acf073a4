# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## 1.0.0 - 2021-03-31

Promoted `0.4.1` to Production! 🍻

## 0.4.1 - 2021-03-29

### Fixed

- [PLCUE-497](https://jira.itg.ti.com/browse/PLCUE-497) Added padding to all numbers.
- [PLCUE-514](https://jira.itg.ti.com/browse/PLCUE-514) When canceling and clicking outside Create Family dialog box,
  error message does not disappear

### Changed

- [PLCUE-514](https://jira.itg.ti.com/browse/PLCUE-514) Device count validation check when creating a new PGS Family.
- [PLCUE-516](https://jira.itg.ti.com/browse/PLCUE-516) Manage Family UI New Devices now indicated with `*`

## 0.4.0 - 2021-03-26

### Added

- [PLCUE-514](https://jira.itg.ti.com/browse/PLCUE-514) Validation check when creating a new PGS Family.
- [PLCUE-516](https://jira.itg.ti.com/browse/PLCUE-516) Legend text on PGS Families Page.

### Changed

- [PLCUE-516](https://jira.itg.ti.com/browse/PLCUE-516) New Families now indicated with `*`.

### Fixed

- [PLCUE-509](https://jira.itg.ti.com/browse/PLCUE-509) Add New Device/Package Parents can now only filter by SBE.
- [PLCUE-512](https://jira.itg.ti.com/browse/PLCUE-512) PGS Header links.

## 0.3.0 - 2021-03-22

UAT Fixes.

### Fixed

- [PLCUE-497](https://jira.itg.ti.com/browse/PLCUE-497) Timestamp seconds format missing `0` padding and CST.

### Changed

- [PLCUE-502](https://jira.itg.ti.com/browse/PLCUE-502) Add New Family button now positioned to the left side of screen.
- [PLCUE-503](https://jira.itg.ti.com/browse/PLCUE-503) Add New Device and Package Parents now filters by SBE.
- [PLCUE-507](https://jira.itg.ti.com/browse/PLCUE-507) All fields now required when creating a new PGS Family.
- [PLCUE-509](https://jira.itg.ti.com/browse/PLCUE-509) Add New Device and Package Parents now filters by AT Site.
- [PLCUE-510](https://jira.itg.ti.com/browse/PLCUE-510) Username now appended to user ID's.
- [PLCUE-511](https://jira.itg.ti.com/browse/PLCUE-511) Added `｜` between `View`, `Manage` actions.

## 0.2.0 - 2021-03-11

UAT Release.

### Added

- [PLCUE-430](https://jira.itg.ti.com/browse/PLCUE-430) Add Package Parent filters are now implemented.
- [PLCUE-431](https://jira.itg.ti.com/browse/PLCUE-431) Add Device filters are now implemented.
- [PLCUE-475](https://jira.itg.ti.com/browse/PLCUE-475) Verbiage indicating auto-save feature.
- [PLCUE-476](https://jira.itg.ti.com/browse/PLCUE-476) API now uses base url from `buildEnvironment`.
- [PLCUE-477](https://jira.itg.ti.com/browse/PLCUE-477) Created/Updated Dates, Created By and Owner data are now
  implemented.
- [PLCUE-493](https://jira.itg.ti.com/browse/PLCUE-493) Links added to open PGS Family Cross Reference views

### Changed

- [PLCUE-474](https://jira.itg.ti.com/browse/PLCUE-474) Adding devices now defaults as included.

## 0.1.0 - 2021-02-22

Demo with Amy and Betty.

### Added

- [PLCUE-434](https://jira.itg.ti.com/browse/PLCUE-434) Implemented SBE2 access.
- [PLCUE-436](https://jira.itg.ti.com/browse/PLCUE-436) Devices in PGS Family Screen gets marked as `NEW`.
- [PLCUE-437](https://jira.itg.ti.com/browse/PLCUE-437) Implemented Remove device action.
- [PLCUE-463](https://jira.itg.ti.com/browse/PLCUE-463) Added Back button on PGS Family Screen.
- [PLCUE-465](https://jira.itg.ti.com/browse/PLCUE-465) Families in PGS Families Screen gets marked as `NEW`.
- [PLCUE-468](https://jira.itg.ti.com/browse/PLCUE-465) Implemented delete PGS Family.

## 0.0.1 - 2020-12-24 🎄

- Project start!
