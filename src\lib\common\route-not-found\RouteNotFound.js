import React from "react";
import { makeStyles } from "@material-ui/styles";
import { Button, Grid, Typography } from "@material-ui/core";
import { NavLink as RouterLink } from "react-router-dom";
import img404 from "./404.png";
import { Home as HomeImg } from "@material-ui/icons";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(4),
  },
  buttonMargin: {
    margin: "10px",
  },
  content: {
    paddingTop: 100,
    textAlign: "center",
  },
  image: {
    marginTop: 10,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
}));

const RouteNotFound = () => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid container justify="center" spacing={4}>
        <Grid item lg={6} xs={12}>
          <div className={classes.content}>
            <Typography variant="h4">
              Sorry, the page can’t be found.
            </Typography>
            <img alt="Page not found" className={classes.image} src={img404} />
            <br />
            <RouterLink to="/home">
              <Button
                className={classes.buttonMargin}
                // variant="contained"
                color="primary"
                startIcon={<HomeImg style={{ fontSize: 40 }} />}
              >
                <Typography>Home</Typography>
              </Button>
            </RouterLink>
          </div>
        </Grid>
      </Grid>
    </div>
  );
};

export default RouteNotFound;
