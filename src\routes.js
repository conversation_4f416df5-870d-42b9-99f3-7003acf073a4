import React, { Suspense } from "react";
// noinspection ES6CheckImport
import {
  BrowserRouter as Router,
  Redirect,
  Route,
  Switch,
} from "react-router-dom";

import Grid from "@material-ui/core/Grid";
import { withStyles } from "@material-ui/core";

import buildEnv from "./buildEnvironment";

// lazy load routes
const ReqTable = React.lazy(() => import("./components/ReqTable"));
const ReqView = React.lazy(() => import("./components/ReqView"));

const { basePath } = buildEnv;

const styles = (theme) => ({
  root: {
    width: "100%",
    flexGrow: 1,
  },
  content: {
    margin: "4px 0px 0px 0px",
    width: `100%`,
    height: "calc(100vh - 48px)", // full screen minus
  },
});

/**
 * Defines the routes of the App. Lazily loads pages. Uses the popular `react-router-dom` library.
 * @param classes material ui classes
 * @returns {JSX.Element} routes controller component
 * <AUTHOR>
 */
const Routes = ({ classes }) => (
  <div className={classes.root}>
    <Grid container spacing={1} className={classes.content}>
      <Grid item xs={12}>
        <Router basename={basePath}>
          <Suspense fallback={<strong>Loading...</strong>}>
            <Switch>
              <Redirect exact from="/" to="/req" />
              <Route exact path="/req" component={ReqTable} />
              <Route exact path="/req/:txId" component={ReqView} />
            </Switch>
          </Suspense>
        </Router>
      </Grid>
    </Grid>
  </div>
);

export default withStyles(styles)(Routes);
