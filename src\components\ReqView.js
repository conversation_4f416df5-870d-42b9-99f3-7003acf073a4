import React, { useContext, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import Alert from "@material-ui/lab/Alert";
import Button from "@material-ui/core/Button";
import Paper from "@material-ui/core/Paper";
import Snackbar from "@material-ui/core/Snackbar";
import Table from "@material-ui/core/Table";
import TableBody from "@material-ui/core/TableBody";
import TableCell from "@material-ui/core/TableCell";
import TableContainer from "@material-ui/core/TableContainer";
import TableRow from "@material-ui/core/TableRow";
import TextField from "@material-ui/core/TextField";
import Typography from "@material-ui/core/Typography";
import { useTheme } from "@material-ui/core/styles";
import { OtpQsn } from "./TypeDefs";
import { UserContext } from "./UserContext";
import {
  defaultError<PERSON><PERSON><PERSON>,
  fetchGet<PERSON><PERSON>,
  fetchPost,
  fetchPost<PERSON><PERSON>,
  useStyles,
} from "./Util";

/** @type {OtpQsn} */
const nullOtpQsn = null;

/** @type {Array<{name: string, value: any}>} */
const emptyFields = [];

/**
 * @param {object} props
 * @param {string} props.txId
 * @param {string} props.txType
 * @returns JSX element
 */
const ReqView = (props) => {
  const classes = useStyles();
  const theme = useTheme();
  const { userContext } = useContext(UserContext);

  const { txId } = useParams();
  const [otpQsn, setOtpQsn] = useState(nullOtpQsn);
  const [price, setPrice] = useState("");
  const [priceEditMode, setPriceEditMode] = useState(false);
  const [wwAccountId, setWwAccountId] = useState("");
  const [wwAccountIdEditMode, setWwAccountIdEditMode] = useState(false);
  const [snackError, setSnackError] = useState(null);
  const [snackSuccess, setSnackSuccess] = useState(null);

  const formatDate = (data) => {
    if (data.createdDate == null) {
      return data;
    }
    return {
      ...data,
      // Remove timestamp from Java Util Date and format
      createdDate: new Date(
        data.createdDate.slice(0, data.createdDate.length - 6)
      ).toLocaleString("en-US"),
    };
  };

  useEffect(() => {
    if (txId) {
      fetchGetJson(
        `/tpld-api/questionnaires/TPLD_CUSTOM_OPN/${txId}`,
        nullOtpQsn,
        "Cannot load OTP QSN"
      )
        .then((data) => {
          setOtpQsn(formatDate(data));
        })
        .catch(defaultErrorHandler);
    }
  }, [txId]);

  useEffect(() => {
    if (otpQsn) {
      setPrice(otpQsn.price);
      setWwAccountId(otpQsn.wwAccountId || "");
    }
  }, [otpQsn]);

  const handleSnackClose = () => {
    setSnackError(null);
    setSnackSuccess(null);
  };

  const priceCancelClicked = () => {
    setPriceEditMode(false);
    setPrice(otpQsn.price);
  };

  const priceEditClicked = () => {
    setPriceEditMode(true);
  };

  const priceSaveClicked = () => {
    fetchPostJson(
      `/tpld-api/questionnaires/TPLD_CUSTOM_OPN/${txId}/price`,
      price,
      null,
      "Cannot save OTP QSN price"
    )
      .then((data) => {
        setPriceEditMode(false);
        setSnackSuccess("Price saved successfully");
        setOtpQsn(formatDate(data));
      })
      .catch((error) => {
        setSnackError("Price cannot be saved");
      });
  };

  const wwAccountIdCancelClicked = () => {
    setWwAccountIdEditMode(false);
    setWwAccountId(otpQsn.wwAccountId || "");
  };

  const wwAccountIdEditClicked = () => {
    setWwAccountIdEditMode(true);
  };

  const fetchPostWwAccountId = () => {
    return fetchPost(
      `/tpld-api/questionnaires/TPLD_CUSTOM_OPN/${txId}/wwAccId`,
      wwAccountId
    ).then((response) => {
      if (!response.ok) {
        return response.json().then((errorData) => {
          throw new Error(
            errorData.message || "Cannot save OTP QSN WW Account ID!"
          );
        });
      }
      return response.json();
    });
  };

  const wwAccountIdSaveClicked = () => {
    fetchPostWwAccountId()
      .then((data) => {
        setWwAccountIdEditMode(false);
        setSnackSuccess("WW Account ID saved successfully");
        setOtpQsn(formatDate(data));
      })
      .catch((error) => {
        console.error(error);
        setSnackError(error.message);
      });
  };

  if (!otpQsn) {
    return <div>Invalid ID</div>;
  }

  const { order, userInfo } = otpQsn.qsnData;
  const { questionnaire } = order;
  const requesterName = `${otpQsn.qsnData.userInfo.firstname} ${otpQsn.qsnData.userInfo.lastname}`;
  const xmlFields = Object.keys(order).filter((key) => key.startsWith("xml"));

  const handleOpen = (content) => {
    content = content
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&apos;");
  const newWindow = window.open("", "_blank");
  newWindow.document.write(`
    <html>
      <head>
        <title>XML Content</title>
      </head>
      <body>
        <pre>${content}</pre>
      </body>
    </html>
  `);
  newWindow.document.close();
  };

  return (
    <div className={classes.fullHeight}>
      <Typography variant="h4">TPLD Request: {txId}</Typography>
      <TableContainer component={Paper} className={classes.requestView}>
        <Table aria-label="simple table">
          <TableBody>
            <TableRow>
              <TableCell component="th">Requester Email</TableCell>
              <TableCell>{userInfo.email}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Requester Name</TableCell>
              <TableCell>{requesterName}</TableCell>
            </TableRow>
            {otpQsn.createdDate && (
              <TableRow>
                <TableCell component="th">Request Date</TableCell>
                <TableCell>{otpQsn.createdDate}</TableCell>
              </TableRow>
            )}
            <TableRow>
              <TableCell component="th">WW Account ID</TableCell>
              <TableCell>
                {userContext.canEditWwAccId && otpQsn.wwAccountId == null ? (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1em",
                    }}
                  >
                    <TextField
                      variant="outlined"
                      style={{
                        minWidth: "200px",
                      }}
                      value={wwAccountId}
                      onChange={(event) => {
                        setWwAccountId(event.target.value);
                      }}
                      disabled={!wwAccountIdEditMode}
                    />
                    {wwAccountIdEditMode ? (
                      <>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={wwAccountIdSaveClicked}
                        >
                          Save
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={wwAccountIdCancelClicked}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="outlined"
                        color="secondary"
                        onClick={wwAccountIdEditClicked}
                      >
                        Edit
                      </Button>
                    )}
                  </div>
                ) : (
                  wwAccountId
                )}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Company</TableCell>
              <TableCell>{otpQsn.companyName}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">OPN</TableCell>
              <TableCell>{order.opn}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">New OPN</TableCell>
              <TableCell>{otpQsn.opnNew}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">New OPN GPN</TableCell>
              <TableCell>{otpQsn.opnNewGpn}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Price</TableCell>
              <TableCell>
                {userContext.canEditPrice ? (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1em",
                    }}
                  >
                    <TextField
                      variant="outlined"
                      style={{
                        minWidth: "200px",
                      }}
                      value={price}
                      onChange={(event) => {
                        setPrice(event.target.value);
                      }}
                      disabled={!priceEditMode}
                    />
                    {priceEditMode ? (
                      <>
                        <Button
                          variant="outlined"
                          color="secondary"
                          onClick={priceSaveClicked}
                        >
                          Save
                        </Button>
                        <Button variant="outlined" onClick={priceCancelClicked}>
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="outlined"
                        color="secondary"
                        onClick={priceEditClicked}
                      >
                        Edit
                      </Button>
                    )}
                  </div>
                ) : (
                  price
                )}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">OTP</TableCell>
              <TableCell>
                <pre>{order.otp}</pre>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Market</TableCell>
              <TableCell>{questionnaire.market?.join(", ")}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">End Equipment</TableCell>
              <TableCell>{questionnaire.endEquipment}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Use Case</TableCell>
              <TableCell>{questionnaire.useCase?.join(", ")}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Military</TableCell>
              <TableCell>{questionnaire.military?.toString()}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Order Type</TableCell>
              <TableCell>{questionnaire.orderType}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Sample Amount</TableCell>
              <TableCell>{questionnaire.sampAmount}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Region</TableCell>
              <TableCell>{questionnaire.region}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">First Name</TableCell>
              <TableCell>{questionnaire.firstName}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Last Name</TableCell>
              <TableCell>{questionnaire.lastName}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Address 1</TableCell>
              <TableCell>{questionnaire.addr1}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Address 2</TableCell>
              <TableCell>{questionnaire.addr2}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Town / City</TableCell>
              <TableCell>{questionnaire.townCity}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">State / Province</TableCell>
              <TableCell>{questionnaire.stateProvince}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Zip / Postal Code</TableCell>
              <TableCell>{questionnaire.zipPostal}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Phone Number</TableCell>
              <TableCell>{questionnaire.phoneNumber}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Production Date</TableCell>
              <TableCell>{questionnaire.prodDate}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell component="th">Yearly Estimated Volume</TableCell>
              <TableCell>{questionnaire.volumeEst}</TableCell>
            </TableRow>
            {xmlFields.map((field) => (
              <TableRow key={field}>
                <TableCell component="th">{field}</TableCell>
                <TableCell>
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      handleOpen(order[field]);
                    }}
                    style={{
                      cursor: "pointer",
                      color: "blue",
                      textDecoration: "underline",
                    }}
                  >
                    View XML
                  </a>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Snackbar
        open={snackError != null || snackSuccess != null}
        onClose={handleSnackClose}
        autoHideDuration={3000}
      >
        <Alert severity={snackError ? "error" : "success"}>
          {snackError ?? snackSuccess}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ReqView;