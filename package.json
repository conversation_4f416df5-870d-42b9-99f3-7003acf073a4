{"name": "@ti/tpld-ui", "version": "1.0.0", "author": "<EMAIL>", "scripts": {"start": "cross-env --env.REACT_APP_ENV=localhost webpack-dev-server", "start:standalone": "webpack-dev-server --env.standalone", "build:dev": "cross-env REACT_APP_ENV=dev webpack --mode=production", "build:stage": "cross-env REACT_APP_ENV=stage webpack --mode=production", "build:prod": "cross-env REACT_APP_ENV=prod webpack --mode=production", "analyze": "webpack --mode=production --env.analyze=true", "lint": "eslint src --ext js", "format": "prettier --write \"./**\"", "test": "cross-env BABEL_ENV=test jest --passWithNoTests", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "coverage": "cross-env BABEL_ENV=test jest --coverage"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.7.5", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.12.11", "@babel/preset-react": "^7.7.4", "@babel/runtime": "^7.8.7", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.2", "@types/jest": "^25.2.1", "@types/react-router-dom": "^5.3.0", "@types/systemjs": "^6.1.0", "babel-eslint": "^11.0.0-beta.2", "babel-jest": "^24.9.0", "babel-plugin-lodash": "^3.3.4", "concurrently": "^5.0.1", "cross-env": "^7.0.2", "dotenv-webpack": "^6.0.2", "eslint": "^6.7.2", "eslint-config-prettier": "^6.7.0", "eslint-config-react-important-stuff": "^2.0.0", "eslint-plugin-prettier": "^3.1.1", "file-loader": "^6.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^25.2.7", "jest-cli": "^25.2.7", "prettier": "^2.0.4", "pretty-quick": "^2.0.1", "single-spa-react": "^2.14.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10", "webpack-config-single-spa-react": "^1.0.3", "webpack-dev-server": "^3.9.0", "webpack-merge": "^4.2.2"}, "dependencies": {"@date-io/moment": "^1.3.13", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "^3.3.10", "@ti/simba-common-util": "^1.0.0", "@ti/tpld-ui": "file:", "ag-grid-community": "^30.2.0", "ag-grid-react": "^30.2.0", "color": "^4.2.3", "lodash": "^4.17.20", "moment": "^2.29.4", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^5.3.0", "react-select": "^5.4.0"}}